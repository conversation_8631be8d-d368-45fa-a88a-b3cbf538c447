<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>扫雷游戏</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Dancing+Script:wght@400;700&family=Pacifico&family=Great+Vibes&family=Allura&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>扫雷游戏</h1>
            <div id="music-notice" style="color: rgba(255,255,255,0.8); font-size: 14px; margin-bottom: 10px; display: none;">
                🎵 背景音乐将自动播放，您可以使用下方按钮控制
            </div>
            <div class="game-info">
                <div class="info-item">
                    <span>雷数: </span>
                    <span id="mine-count">10</span>
                </div>
                <div class="info-item">
                    <span>时间: </span>
                    <span id="timer">0</span>
                </div>
                <div class="info-item">
                    <button id="reset-btn">重新开始</button>
                </div>
                <div class="info-item">
                    <button id="play-btn">▶️ 播放</button>
                    <button id="pause-btn" style="display: none;">⏸️ 暂停</button>
                </div>
                <div class="info-item volume-control">
                    <span class="volume-label">🔊 音量:</span>
                    <input type="range" id="volume-slider" min="0" max="100" value="30" class="volume-slider">
                    <span id="volume-value" class="volume-value">30%</span>
                </div>
            </div>
            <div class="difficulty-selector">
                <button class="difficulty-btn active" data-difficulty="easy">简单 (9x9, 10雷)</button>
                <button class="difficulty-btn" data-difficulty="medium">中等 (16x16, 40雷)</button>
                <button class="difficulty-btn" data-difficulty="hard">困难 (24x20, 99雷)</button>
                <button class="difficulty-btn" data-difficulty="custom">自定义</button>
            </div>

            <div id="custom-settings" class="custom-settings" style="display: none;">
                <div class="custom-input-group">
                    <label for="custom-rows">行数:</label>
                    <input type="number" id="custom-rows" min="5" max="30" value="10">
                </div>
                <div class="custom-input-group">
                    <label for="custom-cols">列数:</label>
                    <input type="number" id="custom-cols" min="5" max="50" value="10">
                </div>
                <div class="custom-input-group">
                    <label for="custom-mines">雷数:</label>
                    <input type="number" id="custom-mines" min="1" max="200" value="15">
                </div>
                <button id="apply-custom" class="apply-custom-btn">应用设置</button>
            </div>
        </header>
        
        <main>
            <div id="game-board" class="game-board"></div>
            <div id="game-status" class="game-status"></div>
        </main>
    </div>
    
    <!-- 背景动画容器 -->
    <div class="background-animation">
        <div class="floating-mines"></div>
        <div class="floating-flags"></div>
        <div class="floating-numbers"></div>
        <div class="grid-lines"></div>
    </div>

    <!-- 隐藏的音频元素用于自动播放 -->
    <audio id="background-music" autoplay loop preload="auto" style="display: none;">
        <source src="Love.mp3" type="audio/mpeg">
        您的浏览器不支持音频播放。
    </audio>

    <!-- 特殊效果音频 -->
    <audio id="magic-music" preload="auto" style="display: none;">
        <source src="sound/mtfk.mp3" type="audio/mpeg">
        您的浏览器不支持音频播放。
    </audio>

    <!-- 特殊按钮 - 左下角 -->
    <button id="magic-button" class="magic-button">
        ✨
    </button>

    <!-- 爱心飘浮容器 -->
    <div id="hearts-container" class="hearts-container"></div>

    <!-- 隐藏的图片 -->
    <div id="hidden-image" class="hidden-image">
        <img src="love.jpeg" alt="Love Image" />
        <p>❤️ 爱心满满 ❤️</p>
    </div>

    <script src="script.js"></script>
</body>
</html>
