class Minesweeper {
    constructor() {
        this.difficulties = {
            easy: { rows: 9, cols: 9, mines: 10, compact: true },
            medium: { rows: 16, cols: 16, mines: 40 },
            hard: { rows: 20, cols: 24, mines: 99 },
            custom: { rows: 10, cols: 10, mines: 15 }
        };

        this.currentDifficulty = 'easy';
        this.board = [];
        this.gameState = 'playing'; // 'playing', 'won', 'lost'
        this.firstClick = true;
        this.timer = 0;
        this.timerInterval = null;
        this.flaggedCount = 0;
        this.musicPlaying = false; // 音乐播放状态

        // 初始化音频
        this.initAudio();

        this.initializeGame();
        this.bindEvents();

        // 初始化音量控制
        this.initVolumeControl();

        // 初始化背景动画
        this.initBackgroundAnimation();

        // 尝试自动播放背景音乐
        this.autoPlayBackgroundMusic();
    }

    initAudio() {
        try {
            this.flagSound = new Audio('sound/flag.MP3');
            this.flagSound.volume = 0.5; // 设置音量为50%
            this.flagSound.preload = 'auto';

            this.boomSound = new Audio('sound/boom.MP3');
            this.boomSound.volume = 0.7; // 爆炸声音稍微大一点
            this.boomSound.preload = 'auto';

            this.clickSound = new Audio('sound/click.MP3');
            this.clickSound.volume = 0.4; // 点击声音稍微小一点
            this.clickSound.preload = 'auto';

            // 使用HTML中的音频元素
            this.bgMusic = document.getElementById('background-music');
            if (this.bgMusic) {
                this.bgMusic.volume = 0.3; // 背景音乐音量较低
            }
        } catch (error) {
            console.warn('无法加载音频文件:', error);
            this.flagSound = null;
            this.boomSound = null;
            this.clickSound = null;
            this.bgMusic = null;
        }
    }

    playFlagSound() {
        if (this.flagSound) {
            try {
                this.flagSound.currentTime = 0; // 重置播放位置
                this.flagSound.play().catch(error => {
                    console.warn('旗帜音频播放失败:', error);
                });
            } catch (error) {
                console.warn('旗帜音频播放错误:', error);
            }
        }
    }

    playBoomSound() {
        if (this.boomSound) {
            try {
                this.boomSound.currentTime = 0; // 重置播放位置
                this.boomSound.play().catch(error => {
                    console.warn('爆炸音频播放失败:', error);
                });
            } catch (error) {
                console.warn('爆炸音频播放错误:', error);
            }
        }
    }

    playClickSound() {
        if (this.clickSound) {
            try {
                this.clickSound.currentTime = 0; // 重置播放位置
                this.clickSound.play().catch(error => {
                    console.warn('点击音频播放失败:', error);
                });
            } catch (error) {
                console.warn('点击音频播放错误:', error);
            }
        }
    }

    autoPlayBackgroundMusic() {
        if (this.bgMusic) {
            // 检查音频是否已经在播放（HTML autoplay可能已经启动）
            if (!this.bgMusic.paused) {
                this.musicPlaying = true;
                this.updateMusicButtons();
                console.log('背景音乐已通过HTML autoplay自动播放');
                return;
            }

            // 立即尝试播放
            const attemptPlay = () => {
                this.bgMusic.play().then(() => {
                    this.musicPlaying = true;
                    this.updateMusicButtons();
                    console.log('背景音乐JavaScript自动播放成功');
                }).catch(error => {
                    console.warn('自动播放被阻止，将在用户交互后播放:', error);
                    this.musicPlaying = false;
                    this.updateMusicButtons();
                    // 添加用户交互监听器
                    this.addUserInteractionListener();
                });
            };

            // 立即尝试播放
            attemptPlay();

            // 监听音频事件
            this.bgMusic.addEventListener('play', () => {
                this.musicPlaying = true;
                this.updateMusicButtons();
            });

            this.bgMusic.addEventListener('pause', () => {
                this.musicPlaying = false;
                this.updateMusicButtons();
            });
        }
    }

    addUserInteractionListener() {
        // 添加用户交互监听器，在用户首次交互后自动播放背景音乐
        const playOnInteraction = () => {
            if (this.bgMusic && !this.musicPlaying) {
                this.bgMusic.play().then(() => {
                    this.musicPlaying = true;
                    this.updateMusicButtons();
                    console.log('用户交互后背景音乐播放成功');
                }).catch(error => {
                    console.warn('用户交互后背景音乐播放失败:', error);
                });
            }
            // 移除监听器，只需要播放一次
            document.removeEventListener('click', playOnInteraction);
            document.removeEventListener('keydown', playOnInteraction);
        };

        document.addEventListener('click', playOnInteraction);
        document.addEventListener('keydown', playOnInteraction);
    }

    updateMusicButtons() {
        const playBtn = document.getElementById('play-btn');
        const pauseBtn = document.getElementById('pause-btn');
        const musicNotice = document.getElementById('music-notice');

        if (this.musicPlaying) {
            playBtn.style.display = 'none';
            pauseBtn.style.display = 'inline-block';
            // 显示音乐提示3秒后隐藏
            if (musicNotice) {
                musicNotice.style.display = 'block';
                setTimeout(() => {
                    musicNotice.style.display = 'none';
                }, 3000);
            }
        } else {
            playBtn.style.display = 'inline-block';
            pauseBtn.style.display = 'none';
        }
    }

    playBoomSound() {
        if (this.boomSound) {
            try {
                this.boomSound.currentTime = 0; // 重置播放位置
                this.boomSound.play().catch(error => {
                    console.warn('爆炸音频播放失败:', error);
                });
            } catch (error) {
                console.warn('爆炸音频播放错误:', error);
            }
        }
    }
    
    initializeGame() {
        const config = this.difficulties[this.currentDifficulty];
        this.rows = config.rows;
        this.cols = config.cols;
        this.totalMines = config.mines;
        this.flaggedCount = 0;
        this.gameState = 'playing';
        this.firstClick = true;
        this.timer = 0;
        
        this.createBoard();
        this.renderBoard();

        // 延迟调整游戏板大小，确保DOM完全渲染
        setTimeout(() => {
            const gameBoard = document.getElementById('game-board');
            this.adjustBoardSize(gameBoard);
        }, 100);

        this.updateMineCount();
        this.updateTimer();
        this.clearGameStatus();
        
        if (this.timerInterval) {
            clearInterval(this.timerInterval);
            this.timerInterval = null;
        }
    }
    
    createBoard() {
        this.board = [];
        for (let row = 0; row < this.rows; row++) {
            this.board[row] = [];
            for (let col = 0; col < this.cols; col++) {
                this.board[row][col] = {
                    isMine: false,
                    isRevealed: false,
                    isFlagged: false,
                    neighborMines: 0
                };
            }
        }
    }
    
    placeMines(excludeRow, excludeCol) {
        let minesPlaced = 0;
        while (minesPlaced < this.totalMines) {
            const row = Math.floor(Math.random() * this.rows);
            const col = Math.floor(Math.random() * this.cols);
            
            if (!this.board[row][col].isMine && 
                !(row === excludeRow && col === excludeCol)) {
                this.board[row][col].isMine = true;
                minesPlaced++;
            }
        }
        
        this.calculateNeighborMines();
    }
    
    calculateNeighborMines() {
        for (let row = 0; row < this.rows; row++) {
            for (let col = 0; col < this.cols; col++) {
                if (!this.board[row][col].isMine) {
                    this.board[row][col].neighborMines = this.countNeighborMines(row, col);
                }
            }
        }
    }
    
    countNeighborMines(row, col) {
        let count = 0;
        for (let i = -1; i <= 1; i++) {
            for (let j = -1; j <= 1; j++) {
                const newRow = row + i;
                const newCol = col + j;
                if (newRow >= 0 && newRow < this.rows && 
                    newCol >= 0 && newCol < this.cols &&
                    this.board[newRow][newCol].isMine) {
                    count++;
                }
            }
        }
        return count;
    }
    
    renderBoard() {
        const gameBoard = document.getElementById('game-board');
        gameBoard.innerHTML = '';
        gameBoard.style.gridTemplateColumns = `repeat(${this.cols}, 1fr)`;

        // 为简单模式设置更小的间距和特殊样式
        const currentConfig = this.difficulties[this.currentDifficulty];
        const isCompactMode = currentConfig.compact || false;
        gameBoard.style.gap = isCompactMode ? '1px' : '3px';

        // 添加或移除compact类
        if (isCompactMode) {
            gameBoard.classList.add('compact');
        } else {
            gameBoard.classList.remove('compact');
        }

        // 根据列数调整游戏板的最大宽度和方块大小
        this.adjustBoardSize(gameBoard);

        for (let row = 0; row < this.rows; row++) {
            for (let col = 0; col < this.cols; col++) {
                const cell = document.createElement('div');
                cell.className = 'cell';
                cell.dataset.row = row;
                cell.dataset.col = col;

                // 添加错开的动画延迟
                const delay = (row * this.cols + col) * 0.01;
                cell.style.animationDelay = `${delay}s`;

                cell.addEventListener('click', (e) => this.handleCellClick(e, row, col));
                cell.addEventListener('contextmenu', (e) => this.handleRightClick(e, row, col));

                gameBoard.appendChild(cell);
            }
        }
    }

    adjustBoardSize(gameBoard) {
        // 获取视窗和容器尺寸
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;
        const container = document.querySelector('.container');
        const containerPadding = 70; // 容器内边距

        // 检查是否为简单模式（紧凑布局）
        const currentConfig = this.difficulties[this.currentDifficulty];
        const isCompactMode = currentConfig.compact || false;

        const extraPadding = isCompactMode ? 30 : 80; // 简单模式进一步减少额外空间
        const gap = isCompactMode ? 1 : 3; // 简单模式最小间距

        // 计算可用空间（减少可用空间以确保有多余空间）
        const availableWidth = Math.min(viewportWidth * 0.85, container.offsetWidth) - containerPadding - extraPadding;
        const availableHeight = viewportHeight * 0.55; // 减少高度限制

        // 根据可用空间计算理想方块大小
        const idealWidthCellSize = Math.floor((availableWidth - (this.cols - 1) * gap) / this.cols);
        const idealHeightCellSize = Math.floor((availableHeight - (this.rows - 1) * gap) / this.rows);

        // 选择较小的尺寸以确保完全适配
        let cellSize = Math.min(idealWidthCellSize, idealHeightCellSize);

        // 设置尺寸限制（简单模式允许更大的方块）
        const maxCellSize = isCompactMode ? 40 : 32;
        const minCellSize = isCompactMode ? 20 : 16;
        cellSize = Math.max(minCellSize, Math.min(maxCellSize, cellSize));

        // 设置CSS变量
        document.documentElement.style.setProperty('--cell-size', `${cellSize}px`);

        // 根据方块大小调整字体（简单模式使用更大字体）
        let fontSize;
        if (isCompactMode) {
            // 简单模式：更大的字体
            if (cellSize >= 35) {
                fontSize = '18px';
            } else if (cellSize >= 30) {
                fontSize = '16px';
            } else if (cellSize >= 25) {
                fontSize = '14px';
            } else {
                fontSize = '12px';
            }
        } else {
            // 其他模式：标准字体
            if (cellSize >= 28) {
                fontSize = '14px';
            } else if (cellSize >= 24) {
                fontSize = '12px';
            } else if (cellSize >= 20) {
                fontSize = '10px';
            } else {
                fontSize = '8px';
            }
        }
        document.documentElement.style.setProperty('--cell-font-size', fontSize);

        // 计算游戏板内容尺寸
        const gridWidth = this.cols * cellSize + (this.cols - 1) * gap;
        const gridHeight = this.rows * cellSize + (this.rows - 1) * gap;

        // 计算游戏板内边距（确保有足够的多余空间）
        let paddingX, paddingY;

        // 根据游戏板大小和屏幕尺寸动态调整内边距
        const isMobile = viewportWidth <= 768;
        const isSmallMobile = viewportWidth <= 480;

        if (isSmallMobile) {
            // 小屏幕设备：紧凑模式进一步减少
            if (isCompactMode) {
                paddingX = Math.max(8, cellSize * 0.3);
                paddingY = Math.max(6, cellSize * 0.25);
            } else {
                paddingX = Math.max(20, cellSize * 0.8);
                paddingY = Math.max(15, cellSize * 0.6);
            }
        } else if (isMobile) {
            // 移动设备：紧凑模式减少内边距
            if (isCompactMode) {
                paddingX = Math.max(10, cellSize * 0.4);
                paddingY = Math.max(8, cellSize * 0.3);
            } else {
                paddingX = Math.max(25, cellSize * 1.0);
                paddingY = Math.max(20, cellSize * 0.8);
            }
        } else {
            // 桌面设备：根据模式调整多余空间
            if (isCompactMode) {
                // 简单模式：极致紧凑的内边距
                if (cellSize >= 35) {
                    paddingX = 15;
                    paddingY = 12;
                } else if (cellSize >= 30) {
                    paddingX = 12;
                    paddingY = 10;
                } else {
                    paddingX = 10;
                    paddingY = 8;
                }
            } else {
                // 其他模式：充足的多余空间
                if (cellSize >= 28) {
                    paddingX = 45;
                    paddingY = 40;
                } else if (cellSize >= 24) {
                    paddingX = 40;
                    paddingY = 35;
                } else if (cellSize >= 20) {
                    paddingX = 35;
                    paddingY = 30;
                } else {
                    paddingX = 30;
                    paddingY = 25;
                }
            }
        }

        // 确保内边距不会太小（紧凑模式允许更小的内边距）
        if (isCompactMode) {
            paddingX = Math.max(paddingX, 8);
            paddingY = Math.max(paddingY, 6);
        } else {
            paddingX = Math.max(paddingX, 20);
            paddingY = Math.max(paddingY, 15);
        }

        // 设置游戏板样式
        gameBoard.style.width = 'auto';
        gameBoard.style.height = 'auto';
        gameBoard.style.maxWidth = '90vw';
        gameBoard.style.maxHeight = '75vh';
        gameBoard.style.padding = `${paddingY}px ${paddingX}px`;

        // 计算总的游戏板尺寸（包含内边距）
        const totalBoardWidth = gridWidth + (paddingX * 2);
        const totalBoardHeight = gridHeight + (paddingY * 2);

        // 如果游戏板太大，启用滚动
        if (totalBoardWidth > viewportWidth * 0.9 || totalBoardHeight > viewportHeight * 0.75) {
            gameBoard.style.overflow = 'auto';
            gameBoard.style.border = '2px solid rgba(139, 92, 246, 0.4)';
            gameBoard.style.maxWidth = '90vw';
            gameBoard.style.maxHeight = '70vh';
        } else {
            gameBoard.style.overflow = 'visible';
            gameBoard.style.border = 'none';
        }

        console.log(`游戏板调整: ${this.cols}x${this.rows}, 方块大小: ${cellSize}px, 字体: ${fontSize}, 内边距: ${paddingX}x${paddingY}px, 紧凑模式: ${isCompactMode}`);
    }
    
    handleCellClick(event, row, col) {
        event.preventDefault();

        if (this.gameState !== 'playing') return;

        const cell = this.board[row][col];
        if (cell.isRevealed || cell.isFlagged) return;

        // 播放点击声音
        this.playClickSound();

        if (this.firstClick) {
            this.placeMines(row, col);
            this.firstClick = false;
            this.startTimer();
        }

        this.revealCell(row, col);
        this.updateDisplay();
        this.checkGameState();
    }
    
    handleRightClick(event, row, col) {
        event.preventDefault();

        if (this.gameState !== 'playing') return;

        const cell = this.board[row][col];
        if (cell.isRevealed) return;

        cell.isFlagged = !cell.isFlagged;
        this.flaggedCount += cell.isFlagged ? 1 : -1;

        // 播放旗帜声音（仅在放置旗帜时播放，移除时不播放）
        if (cell.isFlagged) {
            this.playFlagSound();
        }

        this.updateDisplay();
        this.updateMineCount();
    }
    
    revealCell(row, col) {
        const cell = this.board[row][col];
        if (cell.isRevealed || cell.isFlagged) return;

        cell.isRevealed = true;

        if (cell.isMine) {
            this.gameState = 'lost';
            this.playBoomSound(); // 播放爆炸声音
            this.revealAllMines();
            return;
        }

        if (cell.neighborMines === 0) {
            for (let i = -1; i <= 1; i++) {
                for (let j = -1; j <= 1; j++) {
                    const newRow = row + i;
                    const newCol = col + j;
                    if (newRow >= 0 && newRow < this.rows &&
                        newCol >= 0 && newCol < this.cols) {
                        this.revealCell(newRow, newCol);
                    }
                }
            }
        }
    }

    revealAllMines() {
        for (let row = 0; row < this.rows; row++) {
            for (let col = 0; col < this.cols; col++) {
                if (this.board[row][col].isMine) {
                    this.board[row][col].isRevealed = true;
                }
            }
        }
    }

    updateDisplay() {
        const gameBoard = document.getElementById('game-board');
        const cells = gameBoard.children;

        for (let i = 0; i < cells.length; i++) {
            const cell = cells[i];
            const row = parseInt(cell.dataset.row);
            const col = parseInt(cell.dataset.col);
            const boardCell = this.board[row][col];

            cell.className = 'cell';
            cell.textContent = '';

            if (boardCell.isFlagged) {
                cell.classList.add('flagged');
            } else if (boardCell.isRevealed) {
                cell.classList.add('revealed');

                if (boardCell.isMine) {
                    cell.classList.add('mine');
                } else if (boardCell.neighborMines > 0) {
                    cell.textContent = boardCell.neighborMines;
                    cell.classList.add(`number-${boardCell.neighborMines}`);
                }
            }
        }
    }

    checkGameState() {
        let revealedCount = 0;
        let correctFlags = 0;

        for (let row = 0; row < this.rows; row++) {
            for (let col = 0; col < this.cols; col++) {
                const cell = this.board[row][col];
                if (cell.isRevealed && !cell.isMine) {
                    revealedCount++;
                }
                if (cell.isFlagged && cell.isMine) {
                    correctFlags++;
                }
            }
        }

        const totalSafeCells = this.rows * this.cols - this.totalMines;

        if (this.gameState === 'lost') {
            this.endGame('lose');
        } else if (revealedCount === totalSafeCells) {
            this.gameState = 'won';
            this.endGame('win');
        }
    }

    endGame(result) {
        if (this.timerInterval) {
            clearInterval(this.timerInterval);
            this.timerInterval = null;
        }

        const statusElement = document.getElementById('game-status');
        if (result === 'win') {
            statusElement.textContent = '🎉 恭喜你赢了！';
            statusElement.className = 'game-status win';
        } else {
            statusElement.textContent = '💥 游戏结束！';
            statusElement.className = 'game-status lose';
        }
    }

    startTimer() {
        this.timerInterval = setInterval(() => {
            this.timer++;
            this.updateTimer();
        }, 1000);
    }

    updateTimer() {
        document.getElementById('timer').textContent = this.timer;
    }

    updateMineCount() {
        const remainingMines = this.totalMines - this.flaggedCount;
        document.getElementById('mine-count').textContent = remainingMines;
    }

    clearGameStatus() {
        const statusElement = document.getElementById('game-status');
        statusElement.textContent = '';
        statusElement.className = 'game-status';
    }

    bindEvents() {
        document.getElementById('reset-btn').addEventListener('click', () => {
            this.initializeGame();
        });

        document.querySelectorAll('.difficulty-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                document.querySelectorAll('.difficulty-btn').forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');
                this.currentDifficulty = e.target.dataset.difficulty;

                // 显示或隐藏自定义设置
                const customSettings = document.getElementById('custom-settings');
                if (this.currentDifficulty === 'custom') {
                    customSettings.style.display = 'block';
                    this.updateCustomInputs();
                } else {
                    customSettings.style.display = 'none';
                    this.initializeGame();
                }
            });
        });

        // 自定义设置应用按钮
        document.getElementById('apply-custom').addEventListener('click', () => {
            this.applyCustomSettings();
        });

        // 自定义输入框实时验证
        ['custom-rows', 'custom-cols', 'custom-mines'].forEach(id => {
            document.getElementById(id).addEventListener('input', () => {
                this.validateCustomInputs();
            });
        });

        // 音乐控制按钮
        document.getElementById('play-btn').addEventListener('click', () => {
            this.playMusic();
        });

        document.getElementById('pause-btn').addEventListener('click', () => {
            this.pauseMusic();
        });

        // 音量滑动条
        document.getElementById('volume-slider').addEventListener('input', (e) => {
            this.adjustVolume(e.target.value);
        });

        // 窗口大小变化时重新调整游戏板
        window.addEventListener('resize', () => {
            if (this.board.length > 0) {
                const gameBoard = document.getElementById('game-board');
                this.adjustBoardSize(gameBoard);
            }
        });
    }

    updateCustomInputs() {
        const config = this.difficulties.custom;
        document.getElementById('custom-rows').value = config.rows;
        document.getElementById('custom-cols').value = config.cols;
        document.getElementById('custom-mines').value = config.mines;
        this.validateCustomInputs();
    }

    validateCustomInputs() {
        const rows = parseInt(document.getElementById('custom-rows').value);
        const cols = parseInt(document.getElementById('custom-cols').value);
        const mines = parseInt(document.getElementById('custom-mines').value);
        const applyBtn = document.getElementById('apply-custom');

        const maxMines = Math.floor((rows * cols) * 0.8); // 最多80%的格子可以是雷
        const minesInput = document.getElementById('custom-mines');

        // 更新雷数输入框的最大值
        minesInput.max = maxMines;

        // 验证输入是否有效
        const isValid = rows >= 5 && rows <= 30 &&
                       cols >= 5 && cols <= 50 &&
                       mines >= 1 && mines <= maxMines;

        applyBtn.disabled = !isValid;
        applyBtn.style.opacity = isValid ? '1' : '0.5';

        // 如果雷数超过最大值，自动调整
        if (mines > maxMines) {
            minesInput.value = maxMines;
        }
    }

    applyCustomSettings() {
        const rows = parseInt(document.getElementById('custom-rows').value);
        const cols = parseInt(document.getElementById('custom-cols').value);
        const mines = parseInt(document.getElementById('custom-mines').value);

        // 更新自定义难度配置
        this.difficulties.custom = { rows, cols, mines };

        // 开始新游戏
        this.initializeGame();
    }

    playMusic() {
        if (this.bgMusic) {
            this.bgMusic.play().then(() => {
                this.musicPlaying = true;
                this.updateMusicButtons();
                console.log('手动播放背景音乐成功');
            }).catch(error => {
                console.warn('手动播放背景音乐失败:', error);
            });
        }
    }

    pauseMusic() {
        if (this.bgMusic) {
            this.bgMusic.pause();
            this.musicPlaying = false;
            this.updateMusicButtons();
            console.log('背景音乐已暂停');
        }
    }

    adjustVolume(value) {
        if (this.bgMusic) {
            const volume = value / 100; // 转换为0-1范围
            this.bgMusic.volume = volume;

            // 更新音量显示
            const volumeValue = document.getElementById('volume-value');
            if (volumeValue) {
                volumeValue.textContent = `${value}%`;
            }

            // 更新音量图标
            this.updateVolumeIcon(value);

            // 保存音量设置到本地存储
            localStorage.setItem('minesweeper-volume', value);

            console.log(`音量调整为: ${value}%`);
        }
    }

    updateVolumeIcon(value) {
        const volumeLabel = document.querySelector('.volume-label');
        if (volumeLabel) {
            if (value == 0) {
                volumeLabel.textContent = '🔇 音量:';
            } else if (value < 30) {
                volumeLabel.textContent = '🔈 音量:';
            } else if (value < 70) {
                volumeLabel.textContent = '🔉 音量:';
            } else {
                volumeLabel.textContent = '🔊 音量:';
            }
        }
    }

    initVolumeControl() {
        const volumeSlider = document.getElementById('volume-slider');
        const volumeValue = document.getElementById('volume-value');

        if (volumeSlider && this.bgMusic) {
            // 从本地存储加载音量设置，默认为30
            const savedVolume = localStorage.getItem('minesweeper-volume');
            const initialVolume = savedVolume ? parseInt(savedVolume) : 30;

            volumeSlider.value = initialVolume;
            this.bgMusic.volume = initialVolume / 100;

            if (volumeValue) {
                volumeValue.textContent = `${initialVolume}%`;
            }

            this.updateVolumeIcon(initialVolume);

            console.log(`音量初始化为: ${initialVolume}%`);
        }
    }

    initBackgroundAnimation() {
        this.createFloatingElements();
        this.createParticleEffect();
    }

    createFloatingElements() {
        const mineContainer = document.querySelector('.floating-mines');
        const flagContainer = document.querySelector('.floating-flags');
        const numberContainer = document.querySelector('.floating-numbers');

        // 创建额外的浮动雷
        for (let i = 0; i < 5; i++) {
            const mine = document.createElement('div');
            mine.textContent = '💣';
            mine.style.position = 'absolute';
            mine.style.fontSize = '28px';
            mine.style.opacity = '0.2';
            mine.style.top = Math.random() * 80 + '%';
            mine.style.left = Math.random() * 80 + '%';
            mine.style.animation = `floatMine ${12 + Math.random() * 8}s ease-in-out infinite`;
            mine.style.animationDelay = Math.random() * 12 + 's';
            mine.style.textShadow = '0 0 10px rgba(255, 0, 0, 0.4)';
            mineContainer.appendChild(mine);
        }

        // 创建额外的浮动旗帜
        for (let i = 0; i < 6; i++) {
            const flag = document.createElement('div');
            flag.textContent = '🚩';
            flag.style.position = 'absolute';
            flag.style.fontSize = '24px';
            flag.style.opacity = '0.18';
            flag.style.top = Math.random() * 80 + '%';
            flag.style.left = Math.random() * 80 + '%';
            flag.style.animation = `floatFlag ${10 + Math.random() * 6}s ease-in-out infinite`;
            flag.style.animationDelay = Math.random() * 10 + 's';
            flag.style.textShadow = '0 0 8px rgba(255, 193, 7, 0.5)';
            flagContainer.appendChild(flag);
        }

        // 创建额外的浮动数字
        const numbers = ['1', '2', '3', '4', '5', '6', '7', '8'];
        for (let i = 0; i < 8; i++) {
            const number = document.createElement('div');
            number.textContent = numbers[Math.floor(Math.random() * numbers.length)];
            number.style.position = 'absolute';
            number.style.fontSize = '22px';
            number.style.fontWeight = 'bold';
            number.style.opacity = '0.12';
            number.style.top = Math.random() * 80 + '%';
            number.style.left = Math.random() * 80 + '%';
            number.style.color = this.getNumberColor(number.textContent);
            number.style.animation = `floatNumber ${15 + Math.random() * 10}s linear infinite`;
            number.style.animationDelay = Math.random() * 15 + 's';
            number.style.textShadow = '0 0 6px currentColor';
            numberContainer.appendChild(number);
        }
    }

    getNumberColor(num) {
        const colors = {
            '1': 'rgba(59, 130, 246, 0.2)',
            '2': 'rgba(34, 197, 94, 0.2)',
            '3': 'rgba(239, 68, 68, 0.2)',
            '4': 'rgba(99, 102, 241, 0.2)',
            '5': 'rgba(168, 85, 247, 0.2)',
            '6': 'rgba(14, 165, 233, 0.2)',
            '7': 'rgba(0, 0, 0, 0.2)',
            '8': 'rgba(107, 114, 128, 0.2)'
        };
        return colors[num] || 'rgba(156, 163, 175, 0.2)';
    }

    createParticleEffect() {
        // 创建粒子效果容器
        const particleContainer = document.createElement('div');
        particleContainer.className = 'particle-container';
        particleContainer.style.position = 'fixed';
        particleContainer.style.top = '0';
        particleContainer.style.left = '0';
        particleContainer.style.width = '100%';
        particleContainer.style.height = '100%';
        particleContainer.style.pointerEvents = 'none';
        particleContainer.style.zIndex = '0';

        document.querySelector('.background-animation').appendChild(particleContainer);

        // 创建粒子
        for (let i = 0; i < 30; i++) {
            setTimeout(() => {
                this.createParticle(particleContainer);
            }, i * 300);
        }

        // 定期创建新粒子
        setInterval(() => {
            this.createParticle(particleContainer);
        }, 2000);
    }

    createParticle(container) {
        const particle = document.createElement('div');
        particle.style.position = 'absolute';
        particle.style.width = '6px';
        particle.style.height = '6px';
        particle.style.background = 'rgba(255, 255, 255, 0.3)';
        particle.style.borderRadius = '50%';
        particle.style.top = Math.random() * 100 + '%';
        particle.style.left = Math.random() * 100 + '%';
        particle.style.boxShadow = '0 0 8px rgba(139, 92, 246, 0.4)';

        const duration = 8 + Math.random() * 12;
        particle.style.animation = `particleFloat ${duration}s linear infinite`;

        container.appendChild(particle);

        // 移除粒子以避免内存泄漏
        setTimeout(() => {
            if (particle.parentNode) {
                particle.parentNode.removeChild(particle);
            }
        }, duration * 1000);
    }
}

// 初始化游戏
document.addEventListener('DOMContentLoaded', () => {
    const game = new Minesweeper();

    // 页面加载完成后再次尝试播放音乐
    setTimeout(() => {
        if (game.bgMusic && game.bgMusic.paused) {
            game.bgMusic.play().then(() => {
                game.musicPlaying = true;
                game.updateMusicButtons();
                console.log('页面加载完成后音乐播放成功');
            }).catch(error => {
                console.warn('页面加载完成后音乐播放失败:', error);
            });
        }
    }, 1000); // 延迟1秒后尝试
});
