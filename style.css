:root {
    --primary-color: #8b5cf6;
    --secondary-color: #7c3aed;
    --cell-size: 32px;
    --cell-font-size: 14px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 重新设计的页面布局 */
body {
    font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #8b5cf6 100%);
    min-height: 100vh;

    /* 新的布局系统 */
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;

    /* 间距和滚动 */
    padding: 20px;
    gap: 20px;
    overflow-x: hidden;
    overflow-y: auto;

    /* 文本渲染优化 */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;

    position: relative;
}

body::before {
    content: 'Eimen';
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(-15deg);
    font-family: 'Great Vibes', '<PERSON>ura', 'Dancing Script', 'Pacifico', 'Brush Script MT', 'Lucida Handwriting', cursive, fantasy;
    font-size: 20vw;
    color: rgba(156, 163, 175, 0.15);
    pointer-events: none;
    z-index: 1;
    font-weight: normal;
    text-shadow:
        0 0 40px rgba(156, 163, 175, 0.1),
        4px 4px 8px rgba(0, 0, 0, 0.2),
        -2px -2px 4px rgba(255, 255, 255, 0.1);
    font-style: italic;
    letter-spacing: -0.05em;
    text-decoration: none;
    font-variant: normal;
    font-feature-settings: "liga" 1, "clig" 1, "calt" 1;
}

/* 背景动画容器 */
.background-animation {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 0;
    overflow: hidden;
}

/* 网格线动画 */
.grid-lines {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(90deg, rgba(255, 255, 255, 0.08) 1px, transparent 1px),
        linear-gradient(rgba(255, 255, 255, 0.08) 1px, transparent 1px);
    background-size: 60px 60px;
    animation: gridMove 15s linear infinite;
}

@keyframes gridMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(50px, 50px); }
}

/* 浮动雷动画 */
.floating-mines {
    position: absolute;
    width: 100%;
    height: 100%;
}

.floating-mines::before,
.floating-mines::after {
    content: '💣';
    position: absolute;
    font-size: 32px;
    opacity: 0.25;
    animation: floatMine 12s ease-in-out infinite;
    text-shadow: 0 0 10px rgba(255, 0, 0, 0.3);
}

.floating-mines::before {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.floating-mines::after {
    top: 70%;
    right: 15%;
    animation-delay: 7s;
}

@keyframes floatMine {
    0%, 100% {
        transform: translateY(0px) rotate(0deg) scale(1);
        opacity: 0.15;
    }
    25% {
        transform: translateY(-30px) rotate(90deg) scale(1.1);
        opacity: 0.35;
    }
    50% {
        transform: translateY(-15px) rotate(180deg) scale(1.2);
        opacity: 0.25;
    }
    75% {
        transform: translateY(-40px) rotate(270deg) scale(1.1);
        opacity: 0.2;
    }
}

/* 浮动旗帜动画 */
.floating-flags {
    position: absolute;
    width: 100%;
    height: 100%;
}

.floating-flags::before,
.floating-flags::after {
    content: '🚩';
    position: absolute;
    font-size: 28px;
    opacity: 0.2;
    animation: floatFlag 10s ease-in-out infinite;
    text-shadow: 0 0 8px rgba(255, 193, 7, 0.4);
}

.floating-flags::before {
    top: 40%;
    left: 80%;
    animation-delay: 3s;
}

.floating-flags::after {
    top: 15%;
    left: 60%;
    animation-delay: 9s;
}

@keyframes floatFlag {
    0%, 100% {
        transform: translateX(0px) scale(1) rotate(0deg);
        opacity: 0.15;
    }
    33% {
        transform: translateX(25px) scale(1.2) rotate(5deg);
        opacity: 0.3;
    }
    66% {
        transform: translateX(-15px) scale(1.1) rotate(-3deg);
        opacity: 0.25;
    }
}

/* 浮动数字动画 */
.floating-numbers {
    position: absolute;
    width: 100%;
    height: 100%;
}

.floating-numbers::before,
.floating-numbers::after {
    position: absolute;
    font-size: 24px;
    font-weight: bold;
    opacity: 0.15;
    animation: floatNumber 15s linear infinite;
    text-shadow: 0 0 6px currentColor;
}

.floating-numbers::before {
    content: '1 2 3';
    top: 60%;
    left: 25%;
    color: rgba(59, 130, 246, 0.3);
    animation-delay: 2s;
}

.floating-numbers::after {
    content: '4 5 6';
    top: 30%;
    right: 30%;
    color: rgba(139, 92, 246, 0.3);
    animation-delay: 10s;
}

@keyframes floatNumber {
    0%, 100% {
        transform: translateY(0px) rotate(0deg) scale(1);
        opacity: 0.1;
    }
    50% {
        transform: translateY(-35px) rotate(8deg) scale(1.3);
        opacity: 0.25;
    }
}

/* 粒子动画 */
@keyframes particleFloat {
    0% {
        transform: translateY(0px) translateX(0px) scale(0);
        opacity: 0;
    }
    10% {
        opacity: 0.6;
        transform: scale(1.5);
    }
    90% {
        opacity: 0.3;
    }
    100% {
        transform: translateY(-100vh) translateX(80px) scale(0);
        opacity: 0;
    }
}

/* 脉冲效果 */
.background-animation::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, rgba(139, 92, 246, 0.15) 0%, rgba(139, 92, 246, 0.05) 50%, transparent 70%);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: pulse 6s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 0.6;
    }
    50% {
        transform: translate(-50%, -50%) scale(2);
        opacity: 0.2;
    }
}

/* 扫描线效果 */
.background-animation::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, transparent, rgba(139, 92, 246, 0.7), rgba(255, 255, 255, 0.3), rgba(139, 92, 246, 0.7), transparent);
    box-shadow: 0 0 10px rgba(139, 92, 246, 0.5);
    animation: scanLine 8s linear infinite;
}

@keyframes scanLine {
    0% {
        left: -100%;
        top: 20%;
    }
    25% {
        left: 100%;
        top: 20%;
    }
    26% {
        left: -100%;
        top: 60%;
    }
    50% {
        left: 100%;
        top: 60%;
    }
    51% {
        left: -100%;
        top: 80%;
    }
    75% {
        left: 100%;
        top: 80%;
    }
    76% {
        left: -100%;
        top: 40%;
    }
    100% {
        left: 100%;
        top: 40%;
    }
}

/* 移除背景板的透明容器 */
.container {
    /* 布局和尺寸 */
    width: 100%;
    max-width: 1200px;
    min-height: calc(100vh - 40px);

    /* 移除背景 */
    background: transparent;
    border: none;

    /* 内容布局 */
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 30px;
    padding: 40px 30px;

    /* 移除视觉效果 */
    box-shadow: none;
    backdrop-filter: none;

    /* 滚动和溢出 */
    overflow: visible;

    /* 层级 */
    position: relative;
    z-index: 2;
}



/* 重新设计的头部区域 */
header {
    text-align: center;
    width: 100%;
    margin-bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

h1 {
    color: #ffffff;
    font-size: clamp(2rem, 5vw, 3.5rem);
    font-weight: 800;
    margin: 0 0 10px 0;
    letter-spacing: -0.02em;

    text-shadow:
        0 2px 4px rgba(0, 0, 0, 0.3),
        0 4px 8px rgba(0, 0, 0, 0.2),
        0 0 20px rgba(255, 255, 255, 0.3);

    position: relative;
}

@keyframes shimmer {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* 透明化的游戏信息区域 */
.game-info {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 16px;
    width: 100%;
    max-width: 800px;
    margin: 0 auto;
    flex-wrap: wrap;
    padding: 20px;

    background: transparent;
    border: none;
    backdrop-filter: none;

    box-shadow: none;
}

/* 透明化的信息项样式 */
.info-item {
    background: transparent;
    padding: 12px 18px;
    border-radius: 12px;
    font-weight: 700;
    color: #ffffff;
    font-size: 14px;

    box-shadow:
        0 4px 12px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: none;

    text-shadow:
        0 2px 4px rgba(0, 0, 0, 0.5),
        0 1px 2px rgba(0, 0, 0, 0.3);

    transition: all 0.2s ease;
}

.info-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
    border-color: rgba(255, 255, 255, 0.5);
    box-shadow:
        0 6px 20px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

#reset-btn {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 25px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
    box-shadow:
        0 4px 15px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

#reset-btn:hover {
    background: linear-gradient(135deg, #059669, #047857);
    transform: translateY(-2px);
    box-shadow:
        0 6px 20px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

#play-btn, #pause-btn {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 25px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
    box-shadow:
        0 4px 15px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    margin: 0 5px;
}

#play-btn:hover, #pause-btn:hover {
    background: linear-gradient(135deg, #7c3aed, #6d28d9);
    transform: translateY(-2px);
    box-shadow:
        0 6px 20px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

#play-btn {
    background: linear-gradient(135deg, #10b981, #059669);
}

#play-btn:hover {
    background: linear-gradient(135deg, #059669, #047857);
}

#pause-btn {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

#pause-btn:hover {
    background: linear-gradient(135deg, #d97706, #b45309);
}

.volume-control {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    min-width: 200px;
}

.volume-label {
    font-size: 14px;
    white-space: nowrap;
}

.volume-slider {
    flex: 1;
    height: 6px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
    outline: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.volume-slider::-webkit-slider-thumb {
    appearance: none;
    width: 18px;
    height: 18px;
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.volume-slider::-webkit-slider-thumb:hover {
    background: linear-gradient(135deg, #7c3aed, #6d28d9);
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

.volume-slider::-moz-range-thumb {
    width: 18px;
    height: 18px;
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    border-radius: 50%;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.volume-slider::-moz-range-thumb:hover {
    background: linear-gradient(135deg, #7c3aed, #6d28d9);
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

.volume-slider::-moz-range-track {
    height: 6px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
    border: none;
}

.volume-value {
    font-size: 12px;
    font-weight: bold;
    min-width: 35px;
    text-align: center;
}

/* 透明化的难度选择器 */
.difficulty-selector {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 12px;
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
    flex-wrap: wrap;
    padding: 16px;

    background: transparent;
    border: none;
    backdrop-filter: none;

    box-shadow: none;
}

/* 透明化的难度按钮 */
.difficulty-btn {
    background: transparent;
    color: #ffffff;
    border: 2px solid rgba(255, 255, 255, 0.4);
    padding: 10px 18px;
    border-radius: 12px;
    cursor: pointer;
    font-weight: 700;
    font-size: 14px;

    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: none;

    text-shadow:
        0 2px 4px rgba(0, 0, 0, 0.5),
        0 1px 2px rgba(0, 0, 0, 0.3);

    box-shadow:
        0 4px 12px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.difficulty-btn:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
    border-color: rgba(255, 255, 255, 0.6);
    color: #ffffff;

    box-shadow:
        0 6px 20px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.difficulty-btn.active {
    background: rgba(255, 255, 255, 0.2);
    color: #ffffff;
    border-color: rgba(255, 255, 255, 0.8);

    text-shadow:
        0 2px 4px rgba(0, 0, 0, 0.6),
        0 1px 2px rgba(0, 0, 0, 0.4);

    box-shadow:
        0 6px 24px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

/* 透明化的自定义设置区域 */
.custom-settings {
    background: transparent;
    border-radius: 15px;
    padding: 20px;
    margin: 15px auto 0 auto;
    backdrop-filter: none;
    box-shadow: none;
    border: 2px solid rgba(255, 255, 255, 0.3);
    max-width: 400px;
    width: 100%;
}

.custom-input-group {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
    color: #ffffff;
    font-weight: bold;
}

.custom-input-group label {
    flex: 1;
    margin-right: 10px;
}

/* 透明化的自定义输入框 */
.custom-input-group input {
    flex: 2;
    padding: 8px 12px;
    border: 2px solid rgba(255, 255, 255, 0.4);
    border-radius: 8px;
    background: transparent;
    color: #ffffff;
    font-weight: bold;
    font-size: 14px;
    transition: all 0.3s ease;

    text-shadow:
        0 1px 2px rgba(0, 0, 0, 0.5);
}

.custom-input-group input:focus {
    outline: none;
    border-color: rgba(255, 255, 255, 0.7);
    background: rgba(255, 255, 255, 0.1);
    box-shadow: 0 0 15px rgba(255, 255, 255, 0.3);
}

.custom-input-group input::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.apply-custom-btn {
    width: 100%;
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 10px;
    cursor: pointer;
    font-weight: bold;
    font-size: 16px;
    transition: all 0.3s ease;
    box-shadow:
        0 4px 15px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.apply-custom-btn:hover {
    background: linear-gradient(135deg, #059669, #047857);
    transform: translateY(-2px);
    box-shadow:
        0 6px 20px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

/* 重新设计的游戏板 - 深色背景突出方块 */
.game-board {
    display: grid;
    gap: 3px;
    justify-content: center;
    align-content: center;

    /* 深色背景突出方块 */
    background: linear-gradient(135deg, #374151 0%, #4b5563 50%, #6b7280 100%);
    border-radius: 16px;
    border: 3px solid rgba(255, 255, 255, 0.2);

    /* 布局和尺寸 */
    padding: 25px;
    margin: 0 auto;
    width: fit-content;
    min-width: 200px;

    /* 强化视觉效果 */
    box-shadow:
        0 25px 80px rgba(0, 0, 0, 0.4),
        0 15px 40px rgba(0, 0, 0, 0.3),
        inset 0 2px 0 rgba(255, 255, 255, 0.15),
        inset 0 0 30px rgba(255, 255, 255, 0.05);

    /* 动画 */
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    /* 层级 */
    position: relative;
    z-index: 1;
}

/* 统一的游戏板悬停效果 */
.game-board:hover {
    box-shadow:
        0 30px 90px rgba(0, 0, 0, 0.35),
        0 18px 45px rgba(0, 0, 0, 0.25),
        inset 0 2px 0 rgba(255, 255, 255, 0.2),
        inset 0 0 40px rgba(255, 255, 255, 0.08);
    transform: translateY(-2px) scale(1.005);
    border-color: rgba(255, 255, 255, 0.3);
}

/* 简单模式也使用相同的悬停效果 */
.game-board.compact:hover {
    box-shadow:
        0 30px 90px rgba(0, 0, 0, 0.35),
        0 18px 45px rgba(0, 0, 0, 0.25),
        inset 0 2px 0 rgba(255, 255, 255, 0.2),
        inset 0 0 40px rgba(255, 255, 255, 0.08) !important;
    transform: translateY(-2px) scale(1.005) !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
}

/* 游戏板加载动画 */
.game-board {
    animation: boardFadeIn 0.5s ease-out;
}

@keyframes boardFadeIn {
    0% {
        opacity: 0;
        transform: translateY(20px) scale(0.95);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* 游戏板边界指示器 */
.game-board::before {
    content: '';
    position: absolute;
    top: 8px;
    left: 8px;
    right: 8px;
    bottom: 8px;
    border: 1px dashed rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    pointer-events: none;
    z-index: 0;
}

/* 统一的简单模式紧凑样式 */
.game-board.compact {
    padding: 20px !important;
    border-radius: 16px !important;
    gap: 3px !important;

    /* 使用与标准模式相同的背景 */
    background: linear-gradient(135deg, #374151 0%, #4b5563 50%, #6b7280 100%) !important;
    border: 3px solid rgba(255, 255, 255, 0.2) !important;

    /* 统一的阴影效果 */
    box-shadow:
        0 25px 80px rgba(0, 0, 0, 0.4),
        0 15px 40px rgba(0, 0, 0, 0.3),
        inset 0 2px 0 rgba(255, 255, 255, 0.15),
        inset 0 0 30px rgba(255, 255, 255, 0.05) !important;
}

/* 统一的简单模式方块样式 */
.game-board.compact .cell {
    /* 使用与标准模式相同的立体效果 */
    background: linear-gradient(145deg, #e2e8f0, #cbd5e1, #94a3b8) !important;
    border: 2px outset #cbd5e1 !important;
    border-radius: 4px !important;

    /* 布局优化 */
    line-height: 1 !important;
    padding: 0 !important;

    /* 统一的立体阴影 */
    box-shadow:
        0 3px 6px rgba(0, 0, 0, 0.12),
        0 1px 3px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.7),
        inset 0 0 8px rgba(255, 255, 255, 0.25) !important;

    /* 过渡优化 */
    transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* 统一的简单模式悬停效果 */
.game-board.compact .cell:hover {
    background: linear-gradient(145deg, #f1f5f9, #e2e8f0, #cbd5e1) !important;
    border-color: #8b5cf6 !important;
    transform: translateY(-0.5px) !important;

    box-shadow:
        0 5px 12px rgba(0, 0, 0, 0.18),
        0 2px 6px rgba(0, 0, 0, 0.12),
        inset 0 1px 0 rgba(255, 255, 255, 0.8),
        inset 0 0 10px rgba(255, 255, 255, 0.35),
        0 0 0 2px rgba(139, 92, 246, 0.3) !important;
}

/* 统一的简单模式已揭开方块样式 */
.game-board.compact .cell.revealed {
    background: #ffffff !important;
    border: 1px inset #d1d5db !important;
    border-radius: 1px !important;
    cursor: default !important;
    color: #1f2937 !important;

    /* 统一的凹陷效果 */
    box-shadow:
        inset 0 3px 6px rgba(0, 0, 0, 0.1),
        inset 0 1px 3px rgba(0, 0, 0, 0.15),
        inset 0 0 8px rgba(0, 0, 0, 0.05) !important;

    transform: none !important;
}

.game-board.compact .cell.revealed:hover {
    background: #ffffff !important;
    border-color: #d1d5db !important;
    transform: none !important;

    /* 保持统一的凹陷效果 */
    box-shadow:
        inset 0 3px 6px rgba(0, 0, 0, 0.1),
        inset 0 1px 3px rgba(0, 0, 0, 0.15),
        inset 0 0 8px rgba(0, 0, 0, 0.05) !important;
}

/* 统一的简单模式标记方块样式 */
.game-board.compact .cell.flagged {
    background: linear-gradient(145deg, #f59e0b, #d97706) !important;
    border: 2px outset #f59e0b !important;
    border-radius: 4px !important;
    color: #ffffff !important;

    box-shadow:
        0 3px 6px rgba(0, 0, 0, 0.15),
        0 1px 3px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.5),
        inset 0 0 8px rgba(255, 255, 255, 0.2) !important;
}

.game-board.compact .cell.flagged:hover {
    background: linear-gradient(145deg, #fbbf24, #f59e0b) !important;
    border-color: #f59e0b !important;
    transform: translateY(-0.5px) !important;

    box-shadow:
        0 5px 12px rgba(0, 0, 0, 0.2),
        0 2px 4px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.6),
        inset 0 0 10px rgba(255, 255, 255, 0.3) !important;
}

/* 简单模式数字颜色优化 */
.game-board.compact .number-1 {
    color: #1e40af !important;
    font-weight: 800 !important;
    text-shadow: 0 1px 1px rgba(30, 64, 175, 0.3) !important;
}

.game-board.compact .number-2 {
    color: #059669 !important;
    font-weight: 800 !important;
    text-shadow: 0 1px 1px rgba(5, 150, 105, 0.3) !important;
}

.game-board.compact .number-3 {
    color: #dc2626 !important;
    font-weight: 800 !important;
    text-shadow: 0 1px 1px rgba(220, 38, 38, 0.3) !important;
}

.game-board.compact .number-4 {
    color: #7c3aed !important;
    font-weight: 800 !important;
    text-shadow: 0 1px 1px rgba(124, 58, 237, 0.3) !important;
}

.game-board.compact .number-5 {
    color: #b45309 !important;
    font-weight: 800 !important;
    text-shadow: 0 1px 1px rgba(180, 83, 9, 0.3) !important;
}

.game-board.compact .number-6 {
    color: #0891b2 !important;
    font-weight: 800 !important;
    text-shadow: 0 1px 1px rgba(8, 145, 178, 0.3) !important;
}

.game-board.compact .number-7 {
    color: #111827 !important;
    font-weight: 800 !important;
    text-shadow: 0 1px 1px rgba(17, 24, 39, 0.4) !important;
}

.game-board.compact .number-8 {
    color: #4b5563 !important;
    font-weight: 800 !important;
    text-shadow: 0 1px 1px rgba(75, 85, 99, 0.3) !important;
}

/* 方块加载动画 */
.cell {
    animation: cellFadeIn 0.3s ease-out;
    animation-fill-mode: both;
}

@keyframes cellFadeIn {
    0% {
        opacity: 0;
        transform: scale(0.8);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}



/* 重新设计的未点开方块样式 */
.cell {
    /* 尺寸和布局 */
    width: var(--cell-size);
    height: var(--cell-size);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

    /* 未点开方块：立体按钮效果 */
    background: linear-gradient(145deg, #e2e8f0, #cbd5e1, #94a3b8);
    border: 2px outset #cbd5e1;
    border-radius: 4px;

    /* 字体和文本 */
    font-weight: 600;
    font-size: var(--cell-font-size);
    line-height: 1;
    color: #374151;

    /* 交互 */
    cursor: pointer;
    user-select: none;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

    /* 层级 */
    z-index: 2;

    /* 立体阴影效果 */
    box-shadow:
        0 4px 8px rgba(0, 0, 0, 0.15),
        0 2px 4px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.7),
        inset 0 0 10px rgba(255, 255, 255, 0.3);
}

/* 未点开方块悬停效果 */
.cell:hover {
    background: linear-gradient(145deg, #f1f5f9, #e2e8f0, #cbd5e1);
    border-color: #8b5cf6;
    transform: translateY(-1px);

    box-shadow:
        0 6px 16px rgba(0, 0, 0, 0.2),
        0 3px 8px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.8),
        inset 0 0 12px rgba(255, 255, 255, 0.4),
        0 0 0 2px rgba(139, 92, 246, 0.3);
}

/* 已揭开方块样式 - 平坦凹陷效果 */
.cell.revealed {
    background: #ffffff;
    border: 1px inset #d1d5db;
    border-radius: 1px;
    cursor: default;
    color: #1f2937;

    /* 凹陷效果 */
    box-shadow:
        inset 0 3px 6px rgba(0, 0, 0, 0.1),
        inset 0 1px 3px rgba(0, 0, 0, 0.15),
        inset 0 0 8px rgba(0, 0, 0, 0.05);

    transform: none;
}

.cell.revealed:hover {
    background: #ffffff;
    border-color: #d1d5db;
    transform: none;

    /* 保持凹陷效果 */
    box-shadow:
        inset 0 3px 6px rgba(0, 0, 0, 0.1),
        inset 0 1px 3px rgba(0, 0, 0, 0.15),
        inset 0 0 8px rgba(0, 0, 0, 0.05);
}

.cell.mine {
    background:
        radial-gradient(circle, #ff4757, #dc3545, #c23616);
    color: white;
    box-shadow:
        0 0 20px rgba(220, 53, 69, 0.6),
        inset 0 0 10px rgba(0, 0, 0, 0.3);
    animation: pulse-mine 1s ease-in-out infinite;
}

@keyframes pulse-mine {
    0%, 100% { box-shadow: 0 0 20px rgba(220, 53, 69, 0.6), inset 0 0 10px rgba(0, 0, 0, 0.3); }
    50% { box-shadow: 0 0 30px rgba(220, 53, 69, 0.8), inset 0 0 15px rgba(0, 0, 0, 0.4); }
}

/* 标记方块样式 */
.cell.flagged {
    background: #f59e0b;
    border: 1.5px solid #d97706;
    color: #ffffff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4);

    box-shadow:
        0 3px 6px rgba(0, 0, 0, 0.15),
        0 1px 3px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.4),
        0 0 0 2px rgba(245, 158, 11, 0.3);
}

.cell.flagged:hover {
    background: #fbbf24;
    border-color: #f59e0b;
    transform: translateY(-1px);

    box-shadow:
        0 4px 8px rgba(0, 0, 0, 0.2),
        0 2px 4px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.5),
        0 0 0 2px rgba(251, 191, 36, 0.4);
}

.cell.flagged::before {
    content: "🚩";
}

.cell.mine.revealed::before {
    content: "💣";
}

/* 雷方块样式 */
.cell.mine.revealed {
    background: #dc2626;
    border: 1.5px solid #991b1b;
    color: #ffffff;
    font-size: calc(var(--cell-font-size) + 2px);
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.6);

    box-shadow:
        0 4px 12px rgba(0, 0, 0, 0.3),
        0 2px 6px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        0 0 0 3px rgba(220, 38, 38, 0.4);

    animation: mineExplode 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* 重新设计的爆炸动画 */
@keyframes mineExplode {
    0% {
        transform: scale(1) rotate(0deg);
        box-shadow:
            0 4px 12px rgba(0, 0, 0, 0.3),
            0 2px 6px rgba(0, 0, 0, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.2),
            0 0 0 3px rgba(220, 38, 38, 0.4);
    }
    25% {
        transform: scale(1.15) rotate(2deg);
        box-shadow:
            0 6px 20px rgba(220, 38, 38, 0.4),
            0 4px 12px rgba(0, 0, 0, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.3),
            0 0 0 5px rgba(220, 38, 38, 0.6);
    }
    50% {
        transform: scale(1.05) rotate(-1deg);
        box-shadow:
            0 8px 25px rgba(220, 38, 38, 0.5),
            0 4px 15px rgba(0, 0, 0, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.2),
            0 0 0 4px rgba(220, 38, 38, 0.5);
    }
    100% {
        transform: scale(1) rotate(0deg);
        box-shadow:
            0 4px 12px rgba(0, 0, 0, 0.3),
            0 2px 6px rgba(0, 0, 0, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.2),
            0 0 0 3px rgba(220, 38, 38, 0.4);
    }
}

/* 重新设计的数字颜色系统 */
.number-1 {
    color: #1e40af;
    font-weight: 700;
    text-shadow: 0 1px 2px rgba(30, 64, 175, 0.2);
}

.number-2 {
    color: #059669;
    font-weight: 700;
    text-shadow: 0 1px 2px rgba(5, 150, 105, 0.2);
}

.number-3 {
    color: #dc2626;
    font-weight: 700;
    text-shadow: 0 1px 2px rgba(220, 38, 38, 0.2);
}

.number-4 {
    color: #7c3aed;
    font-weight: 700;
    text-shadow: 0 1px 2px rgba(124, 58, 237, 0.2);
}

.number-5 {
    color: #b45309;
    font-weight: 700;
    text-shadow: 0 1px 2px rgba(180, 83, 9, 0.2);
}

.number-6 {
    color: #0891b2;
    font-weight: 700;
    text-shadow: 0 1px 2px rgba(8, 145, 178, 0.2);
}

.number-7 {
    color: #111827;
    font-weight: 700;
    text-shadow: 0 1px 2px rgba(17, 24, 39, 0.3);
}

.number-8 {
    color: #4b5563;
    font-weight: 700;
    text-shadow: 0 1px 2px rgba(75, 85, 99, 0.2);
}

.game-status {
    text-align: center;
    margin-top: 20px;
    font-size: 18px;
    font-weight: bold;
    min-height: 25px;
}

.game-status.win {
    color: #28a745;
}

.game-status.lose {
    color: #dc3545;
}

/* 重新设计的移动端响应式布局 */
@media (max-width: 768px) {
    body {
        padding: 15px;
        gap: 15px;
    }

    .container {
        padding: 25px 20px;
        gap: 20px;
        max-width: 100%;
        min-height: auto;
        border-radius: 20px;
    }

    h1 {
        font-size: clamp(1.8rem, 4vw, 2.5rem);
        margin-bottom: 5px;
    }

    .game-info {
        gap: 10px;
        padding: 16px;
        flex-wrap: wrap;
        justify-content: center;
        background: transparent;
        border: none;
    }

    .info-item {
        padding: 10px 14px;
        font-size: 13px;
        border-radius: 10px;
        background: transparent;
        border: 2px solid rgba(255, 255, 255, 0.3);
    }

    .difficulty-selector {
        padding: 12px;
        gap: 8px;
        flex-direction: column;
        align-items: stretch;
        background: transparent;
        border: none;
    }

    .difficulty-btn {
        width: 100%;
        max-width: none;
        padding: 12px 16px;
        border-radius: 10px;
    }

    .custom-input-group {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    .custom-input-group label {
        margin-right: 0;
        text-align: center;
        font-size: 14px;
    }

    .volume-control {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
        min-width: auto;
    }

    .volume-label {
        text-align: center;
        font-size: 14px;
    }

    /* 统一的移动端游戏板样式 */
    .game-board {
        padding: 20px 25px;
        margin: 15px auto;
        max-width: 95vw;
        max-height: 65vh;
        border-radius: 16px;

        /* 保持统一的背景和边框 */
        background: linear-gradient(135deg, #374151 0%, #4b5563 50%, #6b7280 100%);
        border: 3px solid rgba(255, 255, 255, 0.2);
    }

    .game-board.compact {
        padding: 15px 18px !important;
        border-radius: 14px !important;

        /* 确保简单模式使用相同的背景 */
        background: linear-gradient(135deg, #374151 0%, #4b5563 50%, #6b7280 100%) !important;
        border: 3px solid rgba(255, 255, 255, 0.2) !important;
    }

    #play-btn, #pause-btn {
        padding: 8px 16px;
        font-size: 14px;
    }

    #reset-btn {
        padding: 8px 16px;
        font-size: 14px;
    }
}

@media (max-width: 480px) {
    body {
        padding: 10px;
        gap: 10px;
    }

    .container {
        padding: 20px 15px;
        gap: 15px;
        border-radius: 16px;
    }

    h1 {
        font-size: clamp(1.5rem, 3.5vw, 2rem);
        margin-bottom: 0;
    }

    .game-info {
        gap: 8px;
        padding: 12px;
        background: transparent;
        border: none;
        justify-content: center;
    }

    .info-item {
        padding: 8px 12px;
        font-size: 12px;
        border-radius: 8px;
        background: transparent;
        border: 2px solid rgba(255, 255, 255, 0.3);
    }

    .difficulty-selector {
        padding: 10px;
        gap: 6px;
        background: transparent;
        border: none;
    }

    .difficulty-btn {
        padding: 10px 14px;
        font-size: 13px;
        border-radius: 8px;
    }

    /* 统一的小屏设备游戏板样式 */
    .game-board {
        padding: 18px;
        border-radius: 14px;
        max-width: 100%;
        max-height: 70vh;
        margin: 0 auto;

        /* 保持统一的背景和边框 */
        background: linear-gradient(135deg, #374151 0%, #4b5563 50%, #6b7280 100%);
        border: 2px solid rgba(255, 255, 255, 0.2);
    }

    .game-board.compact {
        padding: 14px !important;
        border-radius: 12px !important;

        /* 确保简单模式使用相同的背景 */
        background: linear-gradient(135deg, #374151 0%, #4b5563 50%, #6b7280 100%) !important;
        border: 2px solid rgba(255, 255, 255, 0.2) !important;
    }
}

/* 特殊按钮样式 */
.magic-button {
    position: fixed;
    bottom: 30px;
    left: 30px;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: none;
    background: linear-gradient(135deg, #ff6b9d, #c44569, #f8b500);
    color: white;
    font-size: 24px;
    cursor: pointer;
    z-index: 1000;
    box-shadow:
        0 8px 25px rgba(255, 107, 157, 0.4),
        0 4px 12px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    animation: magicPulse 2s ease-in-out infinite;
}

.magic-button:hover {
    transform: scale(1.1) rotate(10deg);
    box-shadow:
        0 12px 35px rgba(255, 107, 157, 0.6),
        0 6px 18px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.4);
    background: linear-gradient(135deg, #ff8fab, #d63384, #ffc107);
}

.magic-button:active {
    transform: scale(0.95) rotate(5deg);
}

@keyframes magicPulse {
    0%, 100% {
        box-shadow:
            0 8px 25px rgba(255, 107, 157, 0.4),
            0 4px 12px rgba(0, 0, 0, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
    }
    50% {
        box-shadow:
            0 12px 35px rgba(255, 107, 157, 0.6),
            0 6px 18px rgba(0, 0, 0, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.4);
    }
}

/* 爱心飘浮容器 */
.hearts-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 999;
    overflow: hidden;
}

/* 爱心样式 */
.floating-heart {
    position: absolute;
    font-size: 30px;
    color: #ff69b4;
    animation: heartFloat 4s ease-out forwards;
    pointer-events: none;
    text-shadow: 0 0 10px rgba(255, 105, 180, 0.6);
}

@keyframes heartFloat {
    0% {
        opacity: 1;
        transform: translateY(0) scale(0.5) rotate(0deg);
    }
    20% {
        opacity: 1;
        transform: translateY(-20vh) scale(1) rotate(10deg);
    }
    80% {
        opacity: 0.8;
        transform: translateY(-80vh) scale(1.2) rotate(-10deg);
    }
    100% {
        opacity: 0;
        transform: translateY(-100vh) scale(0.8) rotate(20deg);
    }
}

/* 隐藏图片样式 */
.hidden-image {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0);
    z-index: 1001;
    text-align: center;
    background: rgba(255, 255, 255, 0.95);
    padding: 40px;
    border-radius: 20px;
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.3),
        0 10px 30px rgba(0, 0, 0, 0.2);
    opacity: 0;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    border: 3px solid rgba(255, 105, 180, 0.3);
}

.hidden-image.show {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
}

.hidden-image img {
    max-width: 100%;
    height: auto;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    margin-bottom: 20px;
}

.hidden-image p {
    font-size: 24px;
    color: #ff69b4;
    font-weight: bold;
    margin: 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    animation: textGlow 2s ease-in-out infinite alternate;
}

@keyframes textGlow {
    0% {
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1), 0 0 10px rgba(255, 105, 180, 0.3);
    }
    100% {
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1), 0 0 20px rgba(255, 105, 180, 0.6);
    }
}

/* 元素消失动画 */
.fade-out-element {
    animation: fadeOutElement 2s ease-out forwards;
}

@keyframes fadeOutElement {
    0% {
        opacity: 1;
        transform: scale(1) rotate(0deg);
    }
    30% {
        opacity: 0.8;
        transform: scale(1.05) rotate(2deg);
    }
    60% {
        opacity: 0.4;
        transform: scale(1.1) rotate(5deg);
    }
    100% {
        opacity: 0;
        transform: scale(0) rotate(15deg);
        pointer-events: none;
    }
}

/* 移动端适配 */
@media (max-width: 768px) {
    .magic-button {
        bottom: 20px;
        left: 20px;
        width: 50px;
        height: 50px;
        font-size: 20px;
    }

    .hidden-image {
        padding: 20px;
        margin: 20px;
        max-width: calc(100% - 40px);
        max-height: calc(100% - 40px);
    }

    .hidden-image img {
        max-width: 100%;
        max-height: 200px;
        object-fit: contain;
    }

    .hidden-image p {
        font-size: 18px;
    }

    .floating-heart {
        font-size: 24px;
    }
}
